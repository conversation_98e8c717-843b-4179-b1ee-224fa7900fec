#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
机构级别综合测试系统
整合三段进阶验证机制，生成完整的测试报告
"""

import os
import sys
import asyncio
import logging
import json
import time
import threading
import concurrent.futures
from typing import Dict, List, Any, Optional
import traceback
import random

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class ComprehensiveInstitutionalTesting:
    def __init__(self):
        self.logger = self._setup_logger()
        self.test_results = {
            "test_start_time": time.time(),
            "test_phases": {},
            "overall_status": "RUNNING",
            "coverage_report": {},
            "error_details": [],
            "performance_metrics": {},
            "quality_assurance": {},
            "production_readiness": {}
        }
        self.results_file = "123/diagnostic_results/comprehensive_test_results.json"
        self._ensure_results_dir()
        
    def _setup_logger(self):
        """设置日志"""
        logger = logging.getLogger("ComprehensiveTesting")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
        
    def _ensure_results_dir(self):
        """确保结果目录存在"""
        os.makedirs(os.path.dirname(self.results_file), exist_ok=True)
        
    def _save_results(self):
        """实时保存测试结果到JSON文件"""
        try:
            self.test_results["last_update"] = time.time()
            with open(self.results_file, 'w', encoding='utf-8') as f:
                json.dump(self.test_results, f, ensure_ascii=False, indent=2, default=str)
        except Exception as e:
            self.logger.error(f"保存测试结果失败: {e}")

    async def phase_3_production_simulation(self):
        """③ 生产环境仿真测试"""
        self.logger.info("🔥 阶段3：生产环境仿真测试开始...")
        
        phase_results = {
            "phase_name": "生产环境仿真测试",
            "start_time": time.time(),
            "tests": {},
            "status": "RUNNING",
            "coverage": 0,
            "success_count": 0,
            "total_count": 0
        }
        
        try:
            # 3.1 真实订单簿测试
            self.logger.info("  🧪 3.1 真实订单簿测试...")
            test_31_result = await self._test_real_orderbook()
            phase_results["tests"]["real_orderbook"] = test_31_result
            
            # 3.2 真实API响应测试
            self.logger.info("  🧪 3.2 真实API响应测试...")
            test_32_result = await self._test_real_api_responses()
            phase_results["tests"]["real_api_responses"] = test_32_result
            
            # 3.3 网络波动模拟测试
            self.logger.info("  🧪 3.3 网络波动模拟测试...")
            test_33_result = await self._test_network_fluctuation()
            phase_results["tests"]["network_fluctuation"] = test_33_result
            
            # 3.4 多任务并发压力测试
            self.logger.info("  🧪 3.4 多任务并发压力测试...")
            test_34_result = await self._test_concurrent_pressure()
            phase_results["tests"]["concurrent_pressure"] = test_34_result
            
            # 3.5 极限滑点与稀有差价场景测试
            self.logger.info("  🧪 3.5 极限滑点与稀有差价场景测试...")
            test_35_result = await self._test_extreme_scenarios()
            phase_results["tests"]["extreme_scenarios"] = test_35_result
            
            # 计算阶段3结果
            all_tests = list(phase_results["tests"].values())
            phase_results["success_count"] = sum(1 for t in all_tests if t.get("status") == "PASS")
            phase_results["total_count"] = len(all_tests)
            phase_results["coverage"] = (phase_results["success_count"] / phase_results["total_count"]) * 100
            phase_results["status"] = "PASS" if phase_results["success_count"] == phase_results["total_count"] else "FAIL"
            
        except Exception as e:
            phase_results["status"] = "ERROR"
            phase_results["error"] = str(e)
            phase_results["traceback"] = traceback.format_exc()
            self.test_results["error_details"].append({
                "phase": "生产环境仿真测试",
                "error": str(e),
                "traceback": traceback.format_exc()
            })
            
        finally:
            phase_results["end_time"] = time.time()
            phase_results["duration"] = phase_results["end_time"] - phase_results["start_time"]
            self.test_results["test_phases"]["phase_3"] = phase_results
            self._save_results()
            
        self.logger.info(f"  📊 阶段3完成: {phase_results['success_count']}/{phase_results['total_count']} 通过")
        return phase_results["status"] == "PASS"

    async def _test_real_orderbook(self):
        """测试真实订单簿"""
        test_result = {
            "test_name": "真实订单簿测试",
            "start_time": time.time(),
            "status": "RUNNING",
            "details": {}
        }
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            
            preloader = get_trading_rules_preloader()
            
            # 测试主流币种的订单簿数据获取
            major_pairs = [
                ("gate", "BTC-USDT", "spot"),
                ("bybit", "BTC-USDT", "futures"),
                ("okx", "ETH-USDT", "spot")
            ]
            
            orderbook_results = {}
            
            for exchange, symbol, market in major_pairs:
                try:
                    # 获取交易规则（这会触发真实API调用）
                    rule = preloader.get_trading_rule(exchange, symbol, market)
                    
                    if rule and rule.qty_step:
                        # 验证精度数据的合理性
                        step_size = float(rule.qty_step)
                        if 0.000001 <= step_size <= 1000:  # 合理的精度范围
                            orderbook_results[f"{exchange}_{symbol}_{market}"] = {
                                "status": "SUCCESS",
                                "step_size": step_size,
                                "source": rule.source,
                                "precision": rule.qty_precision
                            }
                        else:
                            orderbook_results[f"{exchange}_{symbol}_{market}"] = {
                                "status": "INVALID_PRECISION",
                                "step_size": step_size
                            }
                    else:
                        orderbook_results[f"{exchange}_{symbol}_{market}"] = {
                            "status": "NO_DATA"
                        }
                        
                except Exception as e:
                    orderbook_results[f"{exchange}_{symbol}_{market}"] = {
                        "status": f"ERROR: {str(e)[:100]}"
                    }
                    
            test_result["details"]["orderbook_data"] = orderbook_results
            
            # 判断结果：至少50%的主流币种能获取到有效数据
            success_count = sum(1 for r in orderbook_results.values() if r.get("status") == "SUCCESS")
            total_count = len(orderbook_results)
            
            test_result["status"] = "PASS" if success_count >= total_count * 0.5 else "FAIL"
            
        except Exception as e:
            test_result["status"] = "ERROR"
            test_result["error"] = str(e)
            
        finally:
            test_result["end_time"] = time.time()
            test_result["duration"] = test_result["end_time"] - test_result["start_time"]
            
        return test_result

    async def _test_real_api_responses(self):
        """测试真实API响应"""
        test_result = {
            "test_name": "真实API响应测试",
            "start_time": time.time(),
            "status": "RUNNING",
            "details": {}
        }
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            from core.trading_system_initializer import get_global_exchanges
            
            preloader = get_trading_rules_preloader()
            global_exchanges = get_global_exchanges()
            
            api_response_results = {}
            
            if global_exchanges:
                for exchange_name, exchange_instance in global_exchanges.items():
                    try:
                        # 测试API连接状态
                        if hasattr(exchange_instance, 'test_connection'):
                            connection_test = await exchange_instance.test_connection()
                            api_response_results[f"{exchange_name}_connection"] = "SUCCESS" if connection_test else "FAIL"
                        else:
                            api_response_results[f"{exchange_name}_connection"] = "NO_TEST_METHOD"
                            
                        # 测试通过预加载器的API调用
                        rule = preloader.get_trading_rule(exchange_name, "BTC-USDT", "spot")
                        api_response_results[f"{exchange_name}_api_call"] = "SUCCESS" if rule else "NO_DATA"
                        
                    except Exception as e:
                        api_response_results[f"{exchange_name}_error"] = f"ERROR: {str(e)[:100]}"
                        
            else:
                api_response_results["global_exchanges"] = "NOT_INITIALIZED"
                
            test_result["details"]["api_responses"] = api_response_results
            
            # 判断结果：至少有一个交易所API正常响应
            has_success = any("SUCCESS" in str(r) for r in api_response_results.values())
            
            test_result["status"] = "PASS" if has_success else "FAIL"
            
        except Exception as e:
            test_result["status"] = "ERROR"
            test_result["error"] = str(e)
            
        finally:
            test_result["end_time"] = time.time()
            test_result["duration"] = test_result["end_time"] - test_result["start_time"]
            
        return test_result

    async def _test_network_fluctuation(self):
        """测试网络波动模拟"""
        test_result = {
            "test_name": "网络波动模拟测试",
            "start_time": time.time(),
            "status": "RUNNING",
            "details": {}
        }
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            
            preloader = get_trading_rules_preloader()
            
            # 模拟网络延迟情况下的系统表现
            network_results = {}
            
            # 快速连续请求模拟网络波动
            for i in range(5):
                start_time = time.time()
                
                try:
                    # 随机选择交易所和币种
                    exchanges = ["gate", "bybit", "okx"]
                    symbols = ["BTC-USDT", "ETH-USDT"]
                    markets = ["spot", "futures"]
                    
                    exchange = random.choice(exchanges)
                    symbol = random.choice(symbols)
                    market = random.choice(markets)
                    
                    rule = preloader.get_trading_rule(exchange, symbol, market)
                    response_time = time.time() - start_time
                    
                    network_results[f"request_{i}"] = {
                        "status": "SUCCESS" if rule else "NO_DATA",
                        "response_time": response_time,
                        "exchange": exchange,
                        "symbol": symbol,
                        "market": market
                    }
                    
                except Exception as e:
                    network_results[f"request_{i}"] = {
                        "status": f"ERROR: {str(e)[:50]}",
                        "response_time": time.time() - start_time
                    }
                    
                # 短暂延迟模拟网络波动
                await asyncio.sleep(0.1)
                
            test_result["details"]["network_fluctuation"] = network_results
            
            # 计算网络性能指标
            successful_requests = [r for r in network_results.values() if "SUCCESS" in r.get("status", "") or "NO_DATA" in r.get("status", "")]
            if successful_requests:
                avg_response_time = sum(r["response_time"] for r in successful_requests) / len(successful_requests)
                max_response_time = max(r["response_time"] for r in successful_requests)
                
                test_result["details"]["performance_metrics"] = {
                    "average_response_time": avg_response_time,
                    "max_response_time": max_response_time,
                    "success_rate": len(successful_requests) / len(network_results)
                }
                
                # 判断结果：成功率>60%且平均响应时间<15秒
                test_result["status"] = "PASS" if len(successful_requests) / len(network_results) > 0.6 and avg_response_time < 15.0 else "FAIL"
            else:
                test_result["status"] = "FAIL"
                
        except Exception as e:
            test_result["status"] = "ERROR"
            test_result["error"] = str(e)
            
        finally:
            test_result["end_time"] = time.time()
            test_result["duration"] = test_result["end_time"] - test_result["start_time"]
            
        return test_result

    async def _test_concurrent_pressure(self):
        """测试多任务并发压力"""
        test_result = {
            "test_name": "多任务并发压力测试",
            "start_time": time.time(),
            "status": "RUNNING",
            "details": {}
        }
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            
            preloader = get_trading_rules_preloader()
            
            # 并发测试参数
            concurrent_tasks = 10
            requests_per_task = 3
            
            async def concurrent_request_task(task_id):
                """单个并发任务"""
                task_results = []
                
                for i in range(requests_per_task):
                    try:
                        start_time = time.time()
                        
                        # 随机请求
                        exchanges = ["gate", "bybit", "okx"]
                        symbols = ["BTC-USDT", "ETH-USDT", "ADA-USDT"]
                        markets = ["spot", "futures"]
                        
                        exchange = random.choice(exchanges)
                        symbol = random.choice(symbols)
                        market = random.choice(markets)
                        
                        rule = preloader.get_trading_rule(exchange, symbol, market)
                        response_time = time.time() - start_time
                        
                        task_results.append({
                            "status": "SUCCESS" if rule else "NO_DATA",
                            "response_time": response_time,
                            "request_id": f"task_{task_id}_req_{i}"
                        })
                        
                    except Exception as e:
                        task_results.append({
                            "status": f"ERROR: {str(e)[:50]}",
                            "response_time": 0,
                            "request_id": f"task_{task_id}_req_{i}"
                        })
                        
                return task_results
            
            # 启动并发任务
            tasks = [concurrent_request_task(i) for i in range(concurrent_tasks)]
            all_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 整理结果
            concurrent_results = {}
            total_requests = 0
            successful_requests = 0
            total_response_time = 0
            
            for task_id, task_results in enumerate(all_results):
                if isinstance(task_results, Exception):
                    concurrent_results[f"task_{task_id}"] = f"EXCEPTION: {str(task_results)[:100]}"
                else:
                    for result in task_results:
                        total_requests += 1
                        if "SUCCESS" in result["status"] or "NO_DATA" in result["status"]:
                            successful_requests += 1
                            total_response_time += result["response_time"]
                            
                    concurrent_results[f"task_{task_id}"] = {
                        "requests": len(task_results),
                        "successes": sum(1 for r in task_results if "SUCCESS" in r["status"] or "NO_DATA" in r["status"])
                    }
                    
            test_result["details"]["concurrent_results"] = concurrent_results
            
            # 计算并发性能指标
            if successful_requests > 0:
                avg_response_time = total_response_time / successful_requests
                success_rate = successful_requests / total_requests
                
                test_result["details"]["concurrent_metrics"] = {
                    "total_requests": total_requests,
                    "successful_requests": successful_requests,
                    "success_rate": success_rate,
                    "average_response_time": avg_response_time
                }
                
                # 判断结果：成功率>50%且平均响应时间<20秒
                test_result["status"] = "PASS" if success_rate > 0.5 and avg_response_time < 20.0 else "FAIL"
            else:
                test_result["status"] = "FAIL"
                
        except Exception as e:
            test_result["status"] = "ERROR"
            test_result["error"] = str(e)
            
        finally:
            test_result["end_time"] = time.time()
            test_result["duration"] = test_result["end_time"] - test_result["start_time"]
            
        return test_result

    async def _test_extreme_scenarios(self):
        """测试极限滑点与稀有差价场景"""
        test_result = {
            "test_name": "极限滑点与稀有差价场景测试",
            "start_time": time.time(),
            "status": "RUNNING",
            "details": {}
        }
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            
            preloader = get_trading_rules_preloader()
            
            # 测试极端场景下的系统稳定性
            extreme_scenarios = [
                # 小众币种
                ("gate", "SPK-USDT", "spot"),
                ("bybit", "ICNT-USDT", "futures"),
                # 非标准格式
                ("bybit", "1000PEPE-USDT", "futures"),
                # 可能不存在的币种
                ("gate", "NONEXISTENT-USDT", "spot"),
                ("okx", "RARE-USDT", "spot")
            ]
            
            extreme_results = {}
            
            for exchange, symbol, market in extreme_scenarios:
                try:
                    start_time = time.time()
                    rule = preloader.get_trading_rule(exchange, symbol, market)
                    response_time = time.time() - start_time
                    
                    if rule and rule.qty_step:
                        step_size = float(rule.qty_step)
                        extreme_results[f"{exchange}_{symbol}_{market}"] = {
                            "status": "SUCCESS",
                            "step_size": step_size,
                            "response_time": response_time,
                            "source": rule.source
                        }
                    else:
                        extreme_results[f"{exchange}_{symbol}_{market}"] = {
                            "status": "NO_DATA",
                            "response_time": response_time
                        }
                        
                except Exception as e:
                    extreme_results[f"{exchange}_{symbol}_{market}"] = {
                        "status": f"ERROR: {str(e)[:100]}",
                        "response_time": 0
                    }
                    
            test_result["details"]["extreme_scenarios"] = extreme_results
            
            # 验证系统在极端情况下的稳定性
            # 关键是系统不应该崩溃，即使数据不可用
            no_crashes = not any("ERROR" in r.get("status", "") and "timeout" not in r.get("status", "").lower() for r in extreme_results.values())
            has_fallback = any("improved_default" in str(r.get("source", "")) for r in extreme_results.values())
            
            test_result["status"] = "PASS" if no_crashes else "FAIL"
            
        except Exception as e:
            test_result["status"] = "ERROR"
            test_result["error"] = str(e)
            
        finally:
            test_result["end_time"] = time.time()
            test_result["duration"] = test_result["end_time"] - test_result["start_time"]
            
        return test_result

    async def generate_quality_assurance_report(self):
        """生成质量保证报告"""
        self.logger.info("📋 生成质量保证报告...")
        
        qa_report = {
            "report_time": time.time(),
            "修复质量评估": {},
            "性能指标": {},
            "稳定性评估": {},
            "生产就绪度": {}
        }
        
        try:
            # 读取之前的测试结果
            phase1_file = "123/diagnostic_results/institutional_test_results.json"
            phase2_file = "123/diagnostic_results/phase_2_3_test_results.json"
            
            phase1_results = {}
            phase2_results = {}
            
            if os.path.exists(phase1_file):
                with open(phase1_file, 'r', encoding='utf-8') as f:
                    phase1_results = json.load(f)
                    
            if os.path.exists(phase2_file):
                with open(phase2_file, 'r', encoding='utf-8') as f:
                    phase2_results = json.load(f)
            
            # 修复质量评估
            qa_report["修复质量评估"] = {
                "硬编码问题修复": "✅ 已确认修复" if phase1_results.get("test_phases", {}).get("phase_1", {}).get("tests", {}).get("fix_stability", {}).get("status") == "PASS" else "❌ 需要检查",
                "默认精度改进": "✅ 已实现改进默认值" if any("FIX_CONFIRMED" in str(v) for v in phase1_results.get("test_phases", {}).get("phase_1", {}).get("tests", {}).get("fix_stability", {}).get("details", {}).get("hardcode_fix", {}).values()) else "❌ 需要检查",
                "关键案例验证": "✅ 关键案例稳定" if all("STABLE" in str(v) for v in phase1_results.get("test_phases", {}).get("phase_1", {}).get("tests", {}).get("fix_stability", {}).get("details", {}).get("critical_cases", {}).values()) else "❌ 需要检查"
            }
            
            # 性能指标
            phase1_coverage = phase1_results.get("coverage_report", {}).get("overall", 0)
            phase2_coverage = phase2_results.get("test_phases", {}).get("phase_2", {}).get("coverage", 0)
            
            qa_report["性能指标"] = {
                "基础测试覆盖率": f"{phase1_coverage}%",
                "系统级联测试覆盖率": f"{phase2_coverage}%",
                "整体测试通过率": f"{(phase1_coverage + phase2_coverage) / 2}%"
            }
            
            # 稳定性评估
            qa_report["稳定性评估"] = {
                "模块导入稳定性": "✅ 稳定" if phase1_results.get("test_phases", {}).get("phase_1", {}).get("tests", {}).get("module_imports", {}).get("status") == "PASS" else "❌ 不稳定",
                "参数验证稳定性": "✅ 稳定" if phase1_results.get("test_phases", {}).get("phase_1", {}).get("tests", {}).get("parameter_validation", {}).get("status") == "PASS" else "❌ 不稳定",
                "错误处理稳定性": "✅ 稳定" if phase1_results.get("test_phases", {}).get("phase_1", {}).get("tests", {}).get("error_handling", {}).get("status") == "PASS" else "❌ 不稳定"
            }
            
            # 生产就绪度
            all_critical_tests_pass = (
                phase1_results.get("test_phases", {}).get("phase_1", {}).get("status") == "PASS" and
                phase1_coverage >= 100
            )
            
            qa_report["生产就绪度"] = {
                "核心功能就绪": "✅ 就绪" if all_critical_tests_pass else "⚠️ 需要进一步测试",
                "建议部署状态": "✅ 可以部署到测试环境" if all_critical_tests_pass else "❌ 需要修复后再部署",
                "风险评估": "🟢 低风险" if all_critical_tests_pass else "🟡 中等风险"
            }
            
            self.test_results["quality_assurance"] = qa_report
            
        except Exception as e:
            self.logger.error(f"生成质量保证报告失败: {e}")
            qa_report["error"] = str(e)
            
        return qa_report

    async def run_comprehensive_testing(self):
        """运行综合测试"""
        self.logger.info("🚀 开始机构级别综合测试...")
        self.logger.info("=" * 80)
        
        try:
            # 阶段3：生产环境仿真测试
            phase3_success = await self.phase_3_production_simulation()
            
            # 生成质量保证报告
            qa_report = await self.generate_quality_assurance_report()
            
            # 计算总体结果
            self.test_results["overall_status"] = "COMPREHENSIVE_COMPLETE"
            self.test_results["test_end_time"] = time.time()
            self.test_results["total_duration"] = self.test_results["test_end_time"] - self.test_results["test_start_time"]
            
            # 计算覆盖率
            if "phase_3" in self.test_results["test_phases"]:
                phase3_coverage = self.test_results["test_phases"]["phase_3"]["coverage"]
                self.test_results["coverage_report"]["phase_3"] = phase3_coverage
                self.test_results["coverage_report"]["comprehensive"] = phase3_coverage
            
            self._save_results()
            
            self.logger.info("🎉 机构级别综合测试完成！")
            self.logger.info(f"📄 详细结果已保存到: {self.results_file}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 综合测试异常: {e}")
            self.test_results["overall_status"] = "ERROR"
            self.test_results["error_details"].append({
                "phase": "comprehensive",
                "error": str(e),
                "traceback": traceback.format_exc()
            })
            self._save_results()
            return False

async def main():
    """主函数"""
    tester = ComprehensiveInstitutionalTesting()
    success = await tester.run_comprehensive_testing()
    
    print(f"\n📄 综合测试结果已保存到: {tester.results_file}")
    print("🔍 请检查JSON文件获取详细测试结果")
    
    if success:
        print("🎉 机构级别综合测试完成！")
    else:
        print("❌ 综合测试失败，请检查详细结果")

if __name__ == "__main__":
    asyncio.run(main())
