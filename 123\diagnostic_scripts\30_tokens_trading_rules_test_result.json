{"test_metadata": {"timestamp": "2025-07-31 14:17:45", "test_type": "30+代币机构级别全面验证", "total_tokens": 35, "exchanges": ["gate", "bybit", "okx"], "market_types": ["spot", "futures"], "test_stages": 3, "initialized_exchanges": ["gate", "bybit", "okx"], "global_exchanges_set": true, "preloader_initialized": true}, "stage_1_basic_core": {"stage": "基础核心测试", "tests": {"get_trading_rule_exists": true, "_get_precision_from_exchange_api_sync_exists": true, "_create_temporary_exchange_instance_sync_exists": true, "_load_single_trading_rule_exists": true, "invalid_params_handling": true, "empty_params_handling": true}, "performance": {}, "errors": [], "success_rate": 0.0}, "stage_2_system_integration": {}, "stage_3_production_simulation": {}, "overall_summary": {}, "critical_issues": [], "performance_metrics": {}, "fix_verification_status": "PENDING"}