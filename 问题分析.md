2025-07-30 18:00:41.166 [ERROR] [ExecutionEngine] ❌ 期货执行失败: OpeningResult(success=False, order_id=None, executed_quantity=0.0, executed_price=0.0, error_message="开仓失败: 无效的订单结果 - {'id': '', 'status': 'failed', 'error': 'Bybit API错误: 10001: Qty invalid'}", execution_time_ms=439.53847885131836, params_used=OpeningOrderParams(symbol='ICNT-USDT', side='sell', order_type='market', quantity='153.307', price=None, market_type='futures', original_quantity=153.307, original_price=None, step_size='0.001', price_step='0.01', exchange_name='bybit'))


2025-07-30 18:01:38.272 [ERROR] [ExecutionEngine] ❌ 现货执行失败: OpeningResult(success=False, order_id=None, executed_quantity=0.0, executed_price=0.0, error_message="开仓失败: 无效的订单结果 - {'id': '', 'status': 'failed', 'error': 'Bybit API错误: 170137: Order quantity has too many decimals.'}", execution_time_ms=267.10033416748047, params_used=OpeningOrderParams(symbol='SPK-USDT', side='buy', order_type='market', quantity='321.995', price=None, market_type='spot', original_quantity=321.995, original_price=None, step_size='0.001', price_step='0.01', exchange_name='bybit'))



已经审查的结果：
1 问题根源：异步调用逻辑设计缺陷，_get_precision_from_exchange_api_sync方法硬编码返回错误的默认值0.001
回退机制缺陷：当API调用失败时，系统使用错误的硬编码默认值，所有交易所都受影响（Gate.io, OKX, Bybit）


2 问题在于：当前的  _get_precision_from_exchange_api_sync 方法完全跳过了API调用，直接返回硬编码的错误默认值！

🎯 正确的缓存+API策略
优先使用缓存 -  get_trading_rule 方法首先检查缓存
缓存未命中时调用API - 当缓存未命中时，调用  _get_precision_from_exchange_api_sync
API失败时使用改进的默认值 - 当API调用失败时，使用基于交易所特性的默认值






