{"global_exchanges": {"status": "问题确认", "issue": "get_global_exchanges返回None", "impact": "交易规则预加载器无法获取交易所实例进行API调用"}, "trading_rules_preloader": {"status": "正常", "test_result": "成功"}, "precision_methods": {"status": "存在", "has_hardcode": false, "method_length": 0}, "error_scenarios": {"status": "已测试", "scenarios_count": 2}, "fix_recommendations": [{"问题": "全局交易所实例未设置", "修复方案": "在系统初始化时调用set_global_exchanges设置全局实例", "修复文件": "123/core/trading_system_initializer.py", "具体修复": "在initialize_all_systems方法中添加set_global_exchanges(self.exchanges)调用"}]}