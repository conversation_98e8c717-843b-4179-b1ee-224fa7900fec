#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
机构级别三段进阶验证机制
① 基础核心测试：模块单元功能验证
② 复杂系统级联测试：模块交互逻辑验证  
③ 生产环境仿真测试：真实环境模拟验证
"""

import os
import sys
import asyncio
import logging
import json
import time
import threading
import concurrent.futures
from typing import Dict, List, Any, Optional
from decimal import Decimal
import traceback

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class InstitutionalGradeTesting:
    def __init__(self):
        self.logger = self._setup_logger()
        self.test_results = {
            "test_start_time": time.time(),
            "test_phases": {},
            "overall_status": "RUNNING",
            "coverage_report": {},
            "error_details": [],
            "performance_metrics": {}
        }
        self.results_file = "123/diagnostic_results/institutional_test_results.json"
        self._ensure_results_dir()
        
    def _setup_logger(self):
        """设置日志"""
        logger = logging.getLogger("InstitutionalTesting")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
        
    def _ensure_results_dir(self):
        """确保结果目录存在"""
        os.makedirs(os.path.dirname(self.results_file), exist_ok=True)
        
    def _save_results(self):
        """实时保存测试结果到JSON文件"""
        try:
            self.test_results["last_update"] = time.time()
            with open(self.results_file, 'w', encoding='utf-8') as f:
                json.dump(self.test_results, f, ensure_ascii=False, indent=2, default=str)
        except Exception as e:
            self.logger.error(f"保存测试结果失败: {e}")

    async def phase_1_basic_core_testing(self):
        """① 基础核心测试：模块单元功能验证"""
        self.logger.info("🔥 阶段1：基础核心测试开始...")
        
        phase_results = {
            "phase_name": "基础核心测试",
            "start_time": time.time(),
            "tests": {},
            "status": "RUNNING",
            "coverage": 0,
            "success_count": 0,
            "total_count": 0
        }
        
        try:
            # 1.1 模块导入和初始化测试
            self.logger.info("  🧪 1.1 模块导入和初始化测试...")
            test_11_result = await self._test_module_imports()
            phase_results["tests"]["module_imports"] = test_11_result
            
            # 1.2 参数输入输出验证测试
            self.logger.info("  🧪 1.2 参数输入输出验证测试...")
            test_12_result = await self._test_parameter_validation()
            phase_results["tests"]["parameter_validation"] = test_12_result
            
            # 1.3 边界检查测试
            self.logger.info("  🧪 1.3 边界检查测试...")
            test_13_result = await self._test_boundary_conditions()
            phase_results["tests"]["boundary_conditions"] = test_13_result
            
            # 1.4 错误处理测试
            self.logger.info("  🧪 1.4 错误处理测试...")
            test_14_result = await self._test_error_handling()
            phase_results["tests"]["error_handling"] = test_14_result
            
            # 1.5 修复点稳定性测试
            self.logger.info("  🧪 1.5 修复点稳定性测试...")
            test_15_result = await self._test_fix_stability()
            phase_results["tests"]["fix_stability"] = test_15_result
            
            # 计算阶段1结果
            all_tests = list(phase_results["tests"].values())
            phase_results["success_count"] = sum(1 for t in all_tests if t.get("status") == "PASS")
            phase_results["total_count"] = len(all_tests)
            phase_results["coverage"] = (phase_results["success_count"] / phase_results["total_count"]) * 100
            phase_results["status"] = "PASS" if phase_results["success_count"] == phase_results["total_count"] else "FAIL"
            
        except Exception as e:
            phase_results["status"] = "ERROR"
            phase_results["error"] = str(e)
            phase_results["traceback"] = traceback.format_exc()
            self.test_results["error_details"].append({
                "phase": "基础核心测试",
                "error": str(e),
                "traceback": traceback.format_exc()
            })
            
        finally:
            phase_results["end_time"] = time.time()
            phase_results["duration"] = phase_results["end_time"] - phase_results["start_time"]
            self.test_results["test_phases"]["phase_1"] = phase_results
            self._save_results()
            
        self.logger.info(f"  📊 阶段1完成: {phase_results['success_count']}/{phase_results['total_count']} 通过")
        return phase_results["status"] == "PASS"

    async def _test_module_imports(self):
        """测试模块导入和初始化"""
        test_result = {
            "test_name": "模块导入和初始化",
            "start_time": time.time(),
            "status": "RUNNING",
            "details": {}
        }
        
        try:
            # 测试关键模块导入
            critical_modules = [
                "core.trading_rules_preloader",
                "core.trading_system_initializer", 
                "core.universal_token_system"
            ]
            
            import_results = {}
            for module_name in critical_modules:
                try:
                    module = __import__(module_name, fromlist=[''])
                    import_results[module_name] = "SUCCESS"
                except Exception as e:
                    import_results[module_name] = f"FAILED: {e}"
                    
            test_result["details"]["imports"] = import_results
            
            # 测试实例创建
            from core.trading_rules_preloader import get_trading_rules_preloader
            from core.trading_system_initializer import get_trading_system_initializer
            from core.universal_token_system import get_universal_token_system
            
            preloader = get_trading_rules_preloader()
            initializer = get_trading_system_initializer()
            token_system = get_universal_token_system()
            
            test_result["details"]["instances"] = {
                "preloader": "SUCCESS" if preloader else "FAILED",
                "initializer": "SUCCESS" if initializer else "FAILED", 
                "token_system": "SUCCESS" if token_system else "FAILED"
            }
            
            # 检查关键方法存在
            key_methods = [
                (preloader, "get_trading_rule"),
                (preloader, "_get_default_precision_info"),
                (preloader, "_get_precision_from_exchange_api_sync")
            ]
            
            method_results = {}
            for obj, method_name in key_methods:
                method_results[method_name] = "SUCCESS" if hasattr(obj, method_name) else "FAILED"
                
            test_result["details"]["methods"] = method_results
            
            # 判断整体结果
            all_success = (
                all(r == "SUCCESS" for r in import_results.values()) and
                all(r == "SUCCESS" for r in test_result["details"]["instances"].values()) and
                all(r == "SUCCESS" for r in method_results.values())
            )
            
            test_result["status"] = "PASS" if all_success else "FAIL"
            
        except Exception as e:
            test_result["status"] = "ERROR"
            test_result["error"] = str(e)
            
        finally:
            test_result["end_time"] = time.time()
            test_result["duration"] = test_result["end_time"] - test_result["start_time"]
            
        return test_result

    async def _test_parameter_validation(self):
        """测试参数输入输出验证"""
        test_result = {
            "test_name": "参数输入输出验证",
            "start_time": time.time(),
            "status": "RUNNING",
            "details": {}
        }
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 测试有效参数
            valid_params = [
                ("gate", "BTC-USDT", "spot"),
                ("bybit", "ETH-USDT", "futures"),
                ("okx", "BTC-USDT", "spot")
            ]
            
            valid_results = {}
            for exchange, symbol, market in valid_params:
                try:
                    result = preloader.get_trading_rule(exchange, symbol, market)
                    valid_results[f"{exchange}_{symbol}_{market}"] = "SUCCESS" if result else "NO_DATA"
                except Exception as e:
                    valid_results[f"{exchange}_{symbol}_{market}"] = f"ERROR: {e}"
                    
            test_result["details"]["valid_params"] = valid_results
            
            # 测试无效参数
            invalid_params = [
                (None, "BTC-USDT", "spot"),
                ("gate", None, "spot"),
                ("gate", "BTC-USDT", None),
                ("", "BTC-USDT", "spot"),
                ("invalid_exchange", "BTC-USDT", "spot"),
                ("gate", "BTC-USDT", "invalid_market")
            ]
            
            invalid_results = {}
            for exchange, symbol, market in invalid_params:
                try:
                    result = preloader.get_trading_rule(exchange, symbol, market)
                    invalid_results[f"{exchange}_{symbol}_{market}"] = "UNEXPECTED_SUCCESS" if result else "EXPECTED_NONE"
                except Exception as e:
                    invalid_results[f"{exchange}_{symbol}_{market}"] = "EXPECTED_ERROR"
                    
            test_result["details"]["invalid_params"] = invalid_results
            
            # 判断结果
            valid_success = any("SUCCESS" in r or "NO_DATA" in r for r in valid_results.values())
            invalid_handled = all("EXPECTED" in r for r in invalid_results.values())
            
            test_result["status"] = "PASS" if valid_success and invalid_handled else "FAIL"
            
        except Exception as e:
            test_result["status"] = "ERROR"
            test_result["error"] = str(e)
            
        finally:
            test_result["end_time"] = time.time()
            test_result["duration"] = test_result["end_time"] - test_result["start_time"]
            
        return test_result

    async def _test_boundary_conditions(self):
        """测试边界条件"""
        test_result = {
            "test_name": "边界条件测试",
            "start_time": time.time(),
            "status": "RUNNING",
            "details": {}
        }
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 测试极端符号名称
            boundary_cases = [
                ("gate", "A-USDT", "spot"),  # 最短符号
                ("gate", "VERYLONGTOKENNAMETEST-USDT", "spot"),  # 长符号
                ("gate", "BTC-USDT", "spot"),  # 标准符号
                ("bybit", "1000PEPE-USDT", "futures"),  # 数字开头
                ("okx", "BTC-USD", "spot")  # 非USDT对
            ]
            
            boundary_results = {}
            for exchange, symbol, market in boundary_cases:
                try:
                    result = preloader.get_trading_rule(exchange, symbol, market)
                    boundary_results[f"{exchange}_{symbol}_{market}"] = "SUCCESS" if result else "NO_DATA"
                except Exception as e:
                    boundary_results[f"{exchange}_{symbol}_{market}"] = f"ERROR: {str(e)[:100]}"
                    
            test_result["details"]["boundary_cases"] = boundary_results
            
            # 测试默认精度信息的边界
            default_precision_cases = [
                ("bybit", "spot"),
                ("bybit", "futures"),
                ("gate", "spot"),
                ("gate", "futures"),
                ("okx", "spot"),
                ("unknown_exchange", "spot")
            ]
            
            precision_results = {}
            for exchange, market in default_precision_cases:
                try:
                    result = preloader._get_default_precision_info(exchange, market)
                    precision_results[f"{exchange}_{market}"] = "SUCCESS" if result and result.get("step_size") else "NO_DATA"
                except Exception as e:
                    precision_results[f"{exchange}_{market}"] = f"ERROR: {str(e)[:100]}"
                    
            test_result["details"]["precision_boundaries"] = precision_results
            
            # 判断结果
            boundary_handled = not any("ERROR" in r for r in boundary_results.values())
            precision_handled = not any("ERROR" in r for r in precision_results.values())
            
            test_result["status"] = "PASS" if boundary_handled and precision_handled else "FAIL"
            
        except Exception as e:
            test_result["status"] = "ERROR"
            test_result["error"] = str(e)
            
        finally:
            test_result["end_time"] = time.time()
            test_result["duration"] = test_result["end_time"] - test_result["start_time"]
            
        return test_result

    async def _test_error_handling(self):
        """测试错误处理"""
        test_result = {
            "test_name": "错误处理测试",
            "start_time": time.time(),
            "status": "RUNNING",
            "details": {}
        }
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 测试各种错误情况的处理
            error_cases = [
                # 类型错误
                (123, "BTC-USDT", "spot"),
                ("gate", 456, "spot"),
                ("gate", "BTC-USDT", 789),
                # 空值错误
                ("", "", ""),
                # 特殊字符
                ("gate", "BTC/USDT", "spot"),
                ("gate", "BTC-USDT", "spot;DROP TABLE"),
            ]
            
            error_handling_results = {}
            for exchange, symbol, market in error_cases:
                try:
                    result = preloader.get_trading_rule(exchange, symbol, market)
                    error_handling_results[f"{type(exchange).__name__}_{type(symbol).__name__}_{type(market).__name__}"] = "HANDLED" if result is None else "UNEXPECTED_SUCCESS"
                except Exception as e:
                    error_handling_results[f"{type(exchange).__name__}_{type(symbol).__name__}_{type(market).__name__}"] = "EXCEPTION_HANDLED"
                    
            test_result["details"]["error_handling"] = error_handling_results
            
            # 测试网络错误模拟（通过无效交易所实例）
            network_error_results = {}
            try:
                # 模拟网络错误情况
                result = preloader._get_precision_from_exchange_api_sync(None, "BTC-USDT", "spot")
                network_error_results["null_exchange"] = "HANDLED" if result is None else "UNEXPECTED_SUCCESS"
            except Exception as e:
                network_error_results["null_exchange"] = "EXCEPTION_HANDLED"
                
            test_result["details"]["network_errors"] = network_error_results
            
            # 判断结果 - 错误应该被优雅处理，不应该导致系统崩溃
            all_handled = all("HANDLED" in r or "EXCEPTION_HANDLED" in r for r in error_handling_results.values())
            network_handled = all("HANDLED" in r or "EXCEPTION_HANDLED" in r for r in network_error_results.values())
            
            test_result["status"] = "PASS" if all_handled and network_handled else "FAIL"
            
        except Exception as e:
            test_result["status"] = "ERROR"
            test_result["error"] = str(e)
            
        finally:
            test_result["end_time"] = time.time()
            test_result["duration"] = test_result["end_time"] - test_result["start_time"]
            
        return test_result

    async def _test_fix_stability(self):
        """测试修复点稳定性"""
        test_result = {
            "test_name": "修复点稳定性测试",
            "start_time": time.time(),
            "status": "RUNNING",
            "details": {}
        }
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 测试关键修复点：_get_default_precision_info不再返回硬编码0.001
            hardcode_fix_results = {}
            
            test_cases = [
                ("bybit", "futures"),
                ("bybit", "spot"),
                ("gate", "futures"),
                ("gate", "spot"),
                ("okx", "spot")
            ]
            
            for exchange, market in test_cases:
                try:
                    result = preloader._get_default_precision_info(exchange, market)
                    step_size = result.get("step_size", 0)
                    source = result.get("source", "")
                    
                    # 检查是否还有硬编码0.001问题
                    if step_size == 0.001 and source == "default":
                        hardcode_fix_results[f"{exchange}_{market}"] = "HARDCODE_DETECTED"
                    elif source == "improved_default":
                        hardcode_fix_results[f"{exchange}_{market}"] = "FIX_CONFIRMED"
                    else:
                        hardcode_fix_results[f"{exchange}_{market}"] = "ACCEPTABLE"
                        
                except Exception as e:
                    hardcode_fix_results[f"{exchange}_{market}"] = f"ERROR: {e}"
                    
            test_result["details"]["hardcode_fix"] = hardcode_fix_results
            
            # 测试关键案例稳定性（问题分析.md中的案例）
            critical_cases_results = {}
            critical_cases = [
                ("SPK-USDT", "gate", "spot"),
                ("ICNT-USDT", "bybit", "futures"),
                ("SPK-USDT", "bybit", "spot")
            ]
            
            for symbol, exchange, market in critical_cases:
                try:
                    result = preloader.get_trading_rule(exchange, symbol, market)
                    if result and result.qty_step:
                        step_size = float(result.qty_step)
                        # 检查是否解决了原始问题
                        if step_size != 0.001:  # 不是硬编码的0.001
                            critical_cases_results[f"{exchange}_{symbol}_{market}"] = "STABLE"
                        else:
                            critical_cases_results[f"{exchange}_{symbol}_{market}"] = "UNSTABLE_HARDCODE"
                    else:
                        critical_cases_results[f"{exchange}_{symbol}_{market}"] = "NO_RESULT"
                except Exception as e:
                    critical_cases_results[f"{exchange}_{symbol}_{market}"] = f"ERROR: {e}"
                    
            test_result["details"]["critical_cases"] = critical_cases_results
            
            # 判断稳定性
            hardcode_fixed = not any("HARDCODE_DETECTED" in r for r in hardcode_fix_results.values())
            critical_stable = all("STABLE" in r for r in critical_cases_results.values())
            
            test_result["status"] = "PASS" if hardcode_fixed and critical_stable else "FAIL"
            
        except Exception as e:
            test_result["status"] = "ERROR"
            test_result["error"] = str(e)
            
        finally:
            test_result["end_time"] = time.time()
            test_result["duration"] = test_result["end_time"] - test_result["start_time"]
            
        return test_result

    async def run_institutional_testing(self):
        """运行完整的机构级别测试"""
        self.logger.info("🚀 开始机构级别三段进阶验证...")
        self.logger.info("=" * 80)
        
        try:
            # 阶段1：基础核心测试
            phase1_success = await self.phase_1_basic_core_testing()
            
            # 实时保存结果
            self._save_results()
            
            if not phase1_success:
                self.logger.error("❌ 阶段1失败，停止后续测试")
                self.test_results["overall_status"] = "FAILED_PHASE_1"
                self._save_results()
                return False
                
            self.logger.info("✅ 阶段1：基础核心测试通过")
            
            # 注意：由于时间限制，这里先实现阶段1
            # 阶段2和3将在后续实现
            
            # 计算总体结果
            self.test_results["overall_status"] = "PHASE_1_COMPLETE"
            self.test_results["test_end_time"] = time.time()
            self.test_results["total_duration"] = self.test_results["test_end_time"] - self.test_results["test_start_time"]
            
            # 计算覆盖率
            phase1_coverage = self.test_results["test_phases"]["phase_1"]["coverage"]
            self.test_results["coverage_report"]["phase_1"] = phase1_coverage
            self.test_results["coverage_report"]["overall"] = phase1_coverage  # 目前只有阶段1
            
            self._save_results()
            
            self.logger.info("🎉 机构级别测试阶段1完成！")
            self.logger.info(f"📊 覆盖率: {phase1_coverage:.1f}%")
            self.logger.info(f"📄 详细结果已保存到: {self.results_file}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 机构级别测试异常: {e}")
            self.test_results["overall_status"] = "ERROR"
            self.test_results["error_details"].append({
                "phase": "overall",
                "error": str(e),
                "traceback": traceback.format_exc()
            })
            self._save_results()
            return False

async def main():
    """主函数"""
    tester = InstitutionalGradeTesting()
    success = await tester.run_institutional_testing()
    
    print(f"\n📄 测试结果已保存到: {tester.results_file}")
    print("🔍 请检查JSON文件获取详细测试结果")
    
    if success:
        print("🎉 机构级别测试阶段1通过！")
    else:
        print("❌ 机构级别测试失败，请检查详细结果")

if __name__ == "__main__":
    asyncio.run(main())
