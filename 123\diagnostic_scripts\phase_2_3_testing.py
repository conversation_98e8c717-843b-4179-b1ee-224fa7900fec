#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
机构级别阶段2和阶段3测试
② 复杂系统级联测试：模块交互逻辑、状态联动、多币种切换、多交易所分支
③ 生产环境仿真测试：真实订单簿、真实API响应、网络波动模拟、多任务并发压力
"""

import os
import sys
import asyncio
import logging
import json
import time
import threading
import concurrent.futures
from typing import Dict, List, Any, Optional
import traceback
import random

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class Phase2And3Testing:
    def __init__(self):
        self.logger = self._setup_logger()
        self.test_results = {
            "test_start_time": time.time(),
            "test_phases": {},
            "overall_status": "RUNNING",
            "coverage_report": {},
            "error_details": [],
            "performance_metrics": {}
        }
        self.results_file = "123/diagnostic_results/phase_2_3_test_results.json"
        self._ensure_results_dir()
        
    def _setup_logger(self):
        """设置日志"""
        logger = logging.getLogger("Phase23Testing")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
        
    def _ensure_results_dir(self):
        """确保结果目录存在"""
        os.makedirs(os.path.dirname(self.results_file), exist_ok=True)
        
    def _save_results(self):
        """实时保存测试结果到JSON文件"""
        try:
            self.test_results["last_update"] = time.time()
            with open(self.results_file, 'w', encoding='utf-8') as f:
                json.dump(self.test_results, f, ensure_ascii=False, indent=2, default=str)
        except Exception as e:
            self.logger.error(f"保存测试结果失败: {e}")

    async def phase_2_system_cascade_testing(self):
        """② 复杂系统级联测试"""
        self.logger.info("🔥 阶段2：复杂系统级联测试开始...")
        
        phase_results = {
            "phase_name": "复杂系统级联测试",
            "start_time": time.time(),
            "tests": {},
            "status": "RUNNING",
            "coverage": 0,
            "success_count": 0,
            "total_count": 0
        }
        
        try:
            # 2.1 模块交互逻辑测试
            self.logger.info("  🧪 2.1 模块交互逻辑测试...")
            test_21_result = await self._test_module_interactions()
            phase_results["tests"]["module_interactions"] = test_21_result
            
            # 2.2 状态联动测试
            self.logger.info("  🧪 2.2 状态联动测试...")
            test_22_result = await self._test_state_linkage()
            phase_results["tests"]["state_linkage"] = test_22_result
            
            # 2.3 多币种切换测试
            self.logger.info("  🧪 2.3 多币种切换测试...")
            test_23_result = await self._test_multi_token_switching()
            phase_results["tests"]["multi_token_switching"] = test_23_result
            
            # 2.4 多交易所分支测试
            self.logger.info("  🧪 2.4 多交易所分支测试...")
            test_24_result = await self._test_multi_exchange_branches()
            phase_results["tests"]["multi_exchange_branches"] = test_24_result
            
            # 2.5 系统协同一致性测试
            self.logger.info("  🧪 2.5 系统协同一致性测试...")
            test_25_result = await self._test_system_consistency()
            phase_results["tests"]["system_consistency"] = test_25_result
            
            # 计算阶段2结果
            all_tests = list(phase_results["tests"].values())
            phase_results["success_count"] = sum(1 for t in all_tests if t.get("status") == "PASS")
            phase_results["total_count"] = len(all_tests)
            phase_results["coverage"] = (phase_results["success_count"] / phase_results["total_count"]) * 100
            phase_results["status"] = "PASS" if phase_results["success_count"] == phase_results["total_count"] else "FAIL"
            
        except Exception as e:
            phase_results["status"] = "ERROR"
            phase_results["error"] = str(e)
            phase_results["traceback"] = traceback.format_exc()
            self.test_results["error_details"].append({
                "phase": "复杂系统级联测试",
                "error": str(e),
                "traceback": traceback.format_exc()
            })
            
        finally:
            phase_results["end_time"] = time.time()
            phase_results["duration"] = phase_results["end_time"] - phase_results["start_time"]
            self.test_results["test_phases"]["phase_2"] = phase_results
            self._save_results()
            
        self.logger.info(f"  📊 阶段2完成: {phase_results['success_count']}/{phase_results['total_count']} 通过")
        return phase_results["status"] == "PASS"

    async def _test_module_interactions(self):
        """测试模块交互逻辑"""
        test_result = {
            "test_name": "模块交互逻辑测试",
            "start_time": time.time(),
            "status": "RUNNING",
            "details": {}
        }
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            from core.trading_system_initializer import get_trading_system_initializer, get_global_exchanges, set_global_exchanges
            from core.universal_token_system import get_universal_token_system
            
            # 测试模块间的协作
            preloader = get_trading_rules_preloader()
            initializer = get_trading_system_initializer()
            token_system = get_universal_token_system()
            
            # 测试全局交易所实例与交易规则预加载器的交互
            global_exchanges = get_global_exchanges()
            if global_exchanges is None:
                # 初始化交易所
                exchanges = await initializer.initialize_exchanges()
                set_global_exchanges(exchanges)
                global_exchanges = get_global_exchanges()
                
            interaction_results = {}
            
            # 测试交易规则预加载器是否能正确使用全局交易所实例
            if global_exchanges:
                for exchange_name in global_exchanges.keys():
                    try:
                        # 测试通过预加载器获取交易规则
                        rule = preloader.get_trading_rule(exchange_name, "BTC-USDT", "spot")
                        interaction_results[f"preloader_{exchange_name}"] = "SUCCESS" if rule else "NO_DATA"
                    except Exception as e:
                        interaction_results[f"preloader_{exchange_name}"] = f"ERROR: {str(e)[:100]}"
                        
            # 测试通用代币系统与交易规则预加载器的交互
            try:
                # 测试符号格式转换
                gate_symbol = token_system.get_exchange_symbol_format("BTC-USDT", "gate", "spot")
                bybit_symbol = token_system.get_exchange_symbol_format("BTC-USDT", "bybit", "futures")
                
                interaction_results["token_system_gate"] = "SUCCESS" if gate_symbol else "FAIL"
                interaction_results["token_system_bybit"] = "SUCCESS" if bybit_symbol else "FAIL"
                
            except Exception as e:
                interaction_results["token_system"] = f"ERROR: {str(e)[:100]}"
                
            test_result["details"]["interactions"] = interaction_results
            
            # 判断结果
            success_count = sum(1 for r in interaction_results.values() if "SUCCESS" in r or "NO_DATA" in r)
            total_count = len(interaction_results)
            
            test_result["status"] = "PASS" if success_count >= total_count * 0.8 else "FAIL"
            
        except Exception as e:
            test_result["status"] = "ERROR"
            test_result["error"] = str(e)
            
        finally:
            test_result["end_time"] = time.time()
            test_result["duration"] = test_result["end_time"] - test_result["start_time"]
            
        return test_result

    async def _test_state_linkage(self):
        """测试状态联动"""
        test_result = {
            "test_name": "状态联动测试",
            "start_time": time.time(),
            "status": "RUNNING",
            "details": {}
        }
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            
            preloader = get_trading_rules_preloader()
            
            # 测试缓存状态联动
            cache_linkage_results = {}
            
            # 第一次获取，应该缓存未命中
            symbol = "TEST-USDT"
            exchange = "gate"
            market = "spot"
            
            # 清除可能的缓存
            cache_key = f"{exchange}_{symbol}_{market}"
            if hasattr(preloader, 'trading_rules') and cache_key in preloader.trading_rules:
                del preloader.trading_rules[cache_key]
                
            # 第一次调用
            start_time = time.time()
            rule1 = preloader.get_trading_rule(exchange, symbol, market)
            first_call_time = time.time() - start_time
            
            # 第二次调用，应该从缓存获取
            start_time = time.time()
            rule2 = preloader.get_trading_rule(exchange, symbol, market)
            second_call_time = time.time() - start_time
            
            cache_linkage_results["first_call"] = "SUCCESS" if rule1 else "NO_DATA"
            cache_linkage_results["second_call"] = "SUCCESS" if rule2 else "NO_DATA"
            cache_linkage_results["cache_performance"] = "IMPROVED" if second_call_time < first_call_time else "NO_IMPROVEMENT"
            
            # 测试统计状态联动
            if hasattr(preloader, 'stats'):
                stats_before = preloader.stats.copy()
                
                # 执行一些操作
                preloader.get_trading_rule("bybit", "ETH-USDT", "futures")
                
                stats_after = preloader.stats.copy()
                
                cache_linkage_results["stats_updated"] = "SUCCESS" if stats_after != stats_before else "NO_CHANGE"
            else:
                cache_linkage_results["stats_updated"] = "NO_STATS"
                
            test_result["details"]["cache_linkage"] = cache_linkage_results
            
            # 判断结果
            success_indicators = ["SUCCESS", "NO_DATA", "IMPROVED", "NO_CHANGE"]
            all_good = all(any(indicator in str(r) for indicator in success_indicators) for r in cache_linkage_results.values())
            
            test_result["status"] = "PASS" if all_good else "FAIL"
            
        except Exception as e:
            test_result["status"] = "ERROR"
            test_result["error"] = str(e)
            
        finally:
            test_result["end_time"] = time.time()
            test_result["duration"] = test_result["end_time"] - test_result["start_time"]
            
        return test_result

    async def _test_multi_token_switching(self):
        """测试多币种切换"""
        test_result = {
            "test_name": "多币种切换测试",
            "start_time": time.time(),
            "status": "RUNNING",
            "details": {}
        }
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            
            preloader = get_trading_rules_preloader()
            
            # 测试多种代币的快速切换
            tokens = ["BTC-USDT", "ETH-USDT", "SPK-USDT", "ICNT-USDT", "ADA-USDT"]
            exchanges = ["gate", "bybit", "okx"]
            markets = ["spot", "futures"]
            
            switching_results = {}
            
            # 快速切换测试
            for i in range(10):  # 10轮快速切换
                token = random.choice(tokens)
                exchange = random.choice(exchanges)
                market = random.choice(markets)
                
                try:
                    start_time = time.time()
                    rule = preloader.get_trading_rule(exchange, token, market)
                    call_time = time.time() - start_time
                    
                    key = f"switch_{i}_{exchange}_{token}_{market}"
                    switching_results[key] = {
                        "status": "SUCCESS" if rule else "NO_DATA",
                        "time": call_time
                    }
                    
                except Exception as e:
                    key = f"switch_{i}_{exchange}_{token}_{market}"
                    switching_results[key] = {
                        "status": f"ERROR: {str(e)[:50]}",
                        "time": 0
                    }
                    
            test_result["details"]["switching"] = switching_results
            
            # 计算性能指标
            successful_calls = [r for r in switching_results.values() if "SUCCESS" in r["status"] or "NO_DATA" in r["status"]]
            if successful_calls:
                avg_time = sum(r["time"] for r in successful_calls) / len(successful_calls)
                max_time = max(r["time"] for r in successful_calls)
                
                test_result["details"]["performance"] = {
                    "average_time": avg_time,
                    "max_time": max_time,
                    "success_rate": len(successful_calls) / len(switching_results)
                }
                
                # 判断结果：成功率>80%且平均响应时间<5秒
                test_result["status"] = "PASS" if len(successful_calls) / len(switching_results) > 0.8 and avg_time < 5.0 else "FAIL"
            else:
                test_result["status"] = "FAIL"
                
        except Exception as e:
            test_result["status"] = "ERROR"
            test_result["error"] = str(e)
            
        finally:
            test_result["end_time"] = time.time()
            test_result["duration"] = test_result["end_time"] - test_result["start_time"]
            
        return test_result

    async def _test_multi_exchange_branches(self):
        """测试多交易所分支"""
        test_result = {
            "test_name": "多交易所分支测试",
            "start_time": time.time(),
            "status": "RUNNING",
            "details": {}
        }
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            
            preloader = get_trading_rules_preloader()
            
            # 测试每个交易所的分支逻辑
            exchanges = ["gate", "bybit", "okx"]
            test_symbol = "BTC-USDT"
            
            branch_results = {}
            
            for exchange in exchanges:
                exchange_results = {}
                
                # 测试现货分支
                try:
                    spot_rule = preloader.get_trading_rule(exchange, test_symbol, "spot")
                    exchange_results["spot"] = "SUCCESS" if spot_rule else "NO_DATA"
                except Exception as e:
                    exchange_results["spot"] = f"ERROR: {str(e)[:50]}"
                    
                # 测试期货分支
                try:
                    futures_rule = preloader.get_trading_rule(exchange, test_symbol, "futures")
                    exchange_results["futures"] = "SUCCESS" if futures_rule else "NO_DATA"
                except Exception as e:
                    exchange_results["futures"] = f"ERROR: {str(e)[:50]}"
                    
                # 测试默认精度分支
                try:
                    default_spot = preloader._get_default_precision_info(exchange, "spot")
                    default_futures = preloader._get_default_precision_info(exchange, "futures")
                    
                    exchange_results["default_spot"] = "SUCCESS" if default_spot and default_spot.get("step_size") else "FAIL"
                    exchange_results["default_futures"] = "SUCCESS" if default_futures and default_futures.get("step_size") else "FAIL"
                    
                except Exception as e:
                    exchange_results["default_precision"] = f"ERROR: {str(e)[:50]}"
                    
                branch_results[exchange] = exchange_results
                
            test_result["details"]["branches"] = branch_results
            
            # 判断结果：每个交易所至少有一个分支成功
            all_exchanges_working = True
            for exchange, results in branch_results.items():
                has_success = any("SUCCESS" in str(r) or "NO_DATA" in str(r) for r in results.values())
                if not has_success:
                    all_exchanges_working = False
                    break
                    
            test_result["status"] = "PASS" if all_exchanges_working else "FAIL"
            
        except Exception as e:
            test_result["status"] = "ERROR"
            test_result["error"] = str(e)
            
        finally:
            test_result["end_time"] = time.time()
            test_result["duration"] = test_result["end_time"] - test_result["start_time"]
            
        return test_result

    async def _test_system_consistency(self):
        """测试系统协同一致性"""
        test_result = {
            "test_name": "系统协同一致性测试",
            "start_time": time.time(),
            "status": "RUNNING",
            "details": {}
        }
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            
            preloader = get_trading_rules_preloader()
            
            # 测试相同请求的一致性
            consistency_results = {}
            
            test_cases = [
                ("gate", "BTC-USDT", "spot"),
                ("bybit", "ETH-USDT", "futures"),
                ("okx", "BTC-USDT", "spot")
            ]
            
            for exchange, symbol, market in test_cases:
                # 多次调用相同请求，检查结果一致性
                results = []
                for i in range(3):
                    try:
                        rule = preloader.get_trading_rule(exchange, symbol, market)
                        if rule:
                            results.append({
                                "step_size": float(rule.qty_step),
                                "source": rule.source,
                                "precision": rule.qty_precision
                            })
                        else:
                            results.append(None)
                    except Exception as e:
                        results.append(f"ERROR: {str(e)[:50]}")
                        
                # 检查一致性
                if all(r == results[0] for r in results):
                    consistency_results[f"{exchange}_{symbol}_{market}"] = "CONSISTENT"
                else:
                    consistency_results[f"{exchange}_{symbol}_{market}"] = "INCONSISTENT"
                    
            test_result["details"]["consistency"] = consistency_results
            
            # 测试默认值一致性
            default_consistency = {}
            for exchange in ["gate", "bybit", "okx"]:
                for market in ["spot", "futures"]:
                    defaults = []
                    for i in range(2):
                        try:
                            default = preloader._get_default_precision_info(exchange, market)
                            defaults.append(default)
                        except Exception as e:
                            defaults.append(f"ERROR: {str(e)[:50]}")
                            
                    if all(d == defaults[0] for d in defaults):
                        default_consistency[f"{exchange}_{market}"] = "CONSISTENT"
                    else:
                        default_consistency[f"{exchange}_{market}"] = "INCONSISTENT"
                        
            test_result["details"]["default_consistency"] = default_consistency
            
            # 判断结果
            all_consistent = (
                all("CONSISTENT" in r for r in consistency_results.values()) and
                all("CONSISTENT" in r for r in default_consistency.values())
            )
            
            test_result["status"] = "PASS" if all_consistent else "FAIL"
            
        except Exception as e:
            test_result["status"] = "ERROR"
            test_result["error"] = str(e)
            
        finally:
            test_result["end_time"] = time.time()
            test_result["duration"] = test_result["end_time"] - test_result["start_time"]
            
        return test_result

    async def run_phase_2_3_testing(self):
        """运行阶段2和3测试"""
        self.logger.info("🚀 开始阶段2和3测试...")
        self.logger.info("=" * 80)
        
        try:
            # 阶段2：复杂系统级联测试
            phase2_success = await self.phase_2_system_cascade_testing()
            
            # 实时保存结果
            self._save_results()
            
            if not phase2_success:
                self.logger.error("❌ 阶段2失败，跳过阶段3")
                self.test_results["overall_status"] = "FAILED_PHASE_2"
                self._save_results()
                return False
                
            self.logger.info("✅ 阶段2：复杂系统级联测试通过")
            
            # 注意：阶段3生产环境仿真测试需要更复杂的实现
            # 这里先标记为计划实现
            self.test_results["test_phases"]["phase_3"] = {
                "phase_name": "生产环境仿真测试",
                "status": "PLANNED",
                "note": "需要真实API环境和网络模拟工具"
            }
            
            # 计算总体结果
            self.test_results["overall_status"] = "PHASE_2_COMPLETE"
            self.test_results["test_end_time"] = time.time()
            self.test_results["total_duration"] = self.test_results["test_end_time"] - self.test_results["test_start_time"]
            
            # 计算覆盖率
            phase2_coverage = self.test_results["test_phases"]["phase_2"]["coverage"]
            self.test_results["coverage_report"]["phase_2"] = phase2_coverage
            self.test_results["coverage_report"]["overall"] = phase2_coverage
            
            self._save_results()
            
            self.logger.info("🎉 阶段2测试完成！")
            self.logger.info(f"📊 覆盖率: {phase2_coverage:.1f}%")
            self.logger.info(f"📄 详细结果已保存到: {self.results_file}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 阶段2和3测试异常: {e}")
            self.test_results["overall_status"] = "ERROR"
            self.test_results["error_details"].append({
                "phase": "overall",
                "error": str(e),
                "traceback": traceback.format_exc()
            })
            self._save_results()
            return False

async def main():
    """主函数"""
    tester = Phase2And3Testing()
    success = await tester.run_phase_2_3_testing()
    
    print(f"\n📄 测试结果已保存到: {tester.results_file}")
    print("🔍 请检查JSON文件获取详细测试结果")
    
    if success:
        print("🎉 阶段2测试通过！")
    else:
        print("❌ 阶段2测试失败，请检查详细结果")

if __name__ == "__main__":
    asyncio.run(main())
