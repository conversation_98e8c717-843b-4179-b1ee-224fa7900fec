#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
精准定位交易规则精度问题诊断脚本
基于问题分析.md中的错误信息进行深度诊断
"""

import os
import sys
import asyncio
import logging
import traceback
from typing import Dict, Any, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class PrecisionErrorDiagnosis:
    def __init__(self):
        self.logger = self._setup_logger()
        self.diagnosis_results = {}
        
    def _setup_logger(self):
        """设置日志"""
        logger = logging.getLogger("PrecisionErrorDiagnosis")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s [%(levelname)s] [%(name)s] %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
        
    async def diagnose_trading_rules_error(self):
        """诊断交易规则获取错误"""
        self.logger.info("🔍 开始诊断交易规则获取错误...")
        
        # 1. 检查全局交易所实例
        await self._check_global_exchanges()
        
        # 2. 检查交易规则预加载器
        await self._check_trading_rules_preloader()
        
        # 3. 检查精度获取方法
        await self._check_precision_methods()
        
        # 4. 模拟错误场景
        await self._simulate_error_scenarios()
        
    async def _check_global_exchanges(self):
        """检查全局交易所实例"""
        self.logger.info("🔍 1. 检查全局交易所实例...")
        
        try:
            from core.trading_system_initializer import get_global_exchanges, set_global_exchanges
            
            # 检查初始状态
            global_exchanges = get_global_exchanges()
            self.logger.info(f"   当前全局交易所实例: {global_exchanges}")
            
            if global_exchanges is None:
                self.logger.warning("   ❌ get_global_exchanges()返回None - 这是问题根源！")
                self.diagnosis_results["global_exchanges"] = {
                    "status": "问题确认",
                    "issue": "get_global_exchanges返回None",
                    "impact": "交易规则预加载器无法获取交易所实例进行API调用"
                }
                
                # 尝试设置模拟实例
                self.logger.info("   🔧 尝试设置模拟交易所实例...")
                mock_exchanges = {
                    'gate': 'MockGateExchange',
                    'bybit': 'MockBybitExchange', 
                    'okx': 'MockOKXExchange'
                }
                set_global_exchanges(mock_exchanges)
                
                # 验证设置结果
                after_set = get_global_exchanges()
                self.logger.info(f"   设置后状态: {after_set}")
                
            else:
                self.logger.info(f"   ✅ 全局交易所实例正常: {list(global_exchanges.keys())}")
                self.diagnosis_results["global_exchanges"] = {
                    "status": "正常",
                    "exchanges": list(global_exchanges.keys())
                }
                
        except Exception as e:
            self.logger.error(f"   ❌ 检查全局交易所实例失败: {e}")
            self.diagnosis_results["global_exchanges"] = {
                "status": "异常",
                "error": str(e)
            }
            
    async def _check_trading_rules_preloader(self):
        """检查交易规则预加载器"""
        self.logger.info("🔍 2. 检查交易规则预加载器...")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            
            preloader = get_trading_rules_preloader()
            self.logger.info("   ✅ 交易规则预加载器获取成功")
            
            # 测试获取交易规则
            test_symbol = "SPK-USDT"
            test_exchange = "gate"
            test_market = "spot"
            
            self.logger.info(f"   🧪 测试获取交易规则: {test_exchange}_{test_symbol}_{test_market}")
            
            rule = preloader.get_trading_rule(test_exchange, test_symbol, test_market)
            
            if rule:
                self.logger.info(f"   ✅ 交易规则获取成功: {rule}")
                self.diagnosis_results["trading_rules_preloader"] = {
                    "status": "正常",
                    "test_result": "成功"
                }
            else:
                self.logger.warning(f"   ❌ 交易规则获取失败: {test_exchange}_{test_symbol}_{test_market}")
                self.diagnosis_results["trading_rules_preloader"] = {
                    "status": "问题确认",
                    "issue": f"无法获取交易规则: {test_exchange}_{test_symbol}_{test_market}",
                    "test_result": "失败"
                }
                
        except Exception as e:
            self.logger.error(f"   ❌ 检查交易规则预加载器失败: {e}")
            self.diagnosis_results["trading_rules_preloader"] = {
                "status": "异常",
                "error": str(e)
            }
            
    async def _check_precision_methods(self):
        """检查精度获取方法"""
        self.logger.info("🔍 3. 检查精度获取方法...")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            
            preloader = get_trading_rules_preloader()
            
            # 检查是否存在_get_precision_from_exchange_api_sync方法
            if hasattr(preloader, '_get_precision_from_exchange_api_sync'):
                self.logger.info("   ✅ _get_precision_from_exchange_api_sync方法存在")
                
                # 检查方法实现
                import inspect
                method_source = inspect.getsource(preloader._get_precision_from_exchange_api_sync)
                
                # 检查是否有硬编码的0.001返回值
                if "0.001" in method_source:
                    self.logger.warning("   ⚠️ 发现方法中包含0.001，可能存在硬编码问题")
                    
                    # 查找具体位置
                    lines = method_source.split('\n')
                    for i, line in enumerate(lines):
                        if "0.001" in line and "return" in line:
                            self.logger.warning(f"   🚨 第{i+1}行发现硬编码返回: {line.strip()}")
                            
                self.diagnosis_results["precision_methods"] = {
                    "status": "存在",
                    "has_hardcode": "0.001" in method_source,
                    "method_length": len(lines) if 'lines' in locals() else 0
                }
            else:
                self.logger.warning("   ❌ _get_precision_from_exchange_api_sync方法不存在")
                self.diagnosis_results["precision_methods"] = {
                    "status": "缺失",
                    "issue": "_get_precision_from_exchange_api_sync方法不存在"
                }
                
        except Exception as e:
            self.logger.error(f"   ❌ 检查精度获取方法失败: {e}")
            self.diagnosis_results["precision_methods"] = {
                "status": "异常",
                "error": str(e)
            }
            
    async def _simulate_error_scenarios(self):
        """模拟错误场景"""
        self.logger.info("🔍 4. 模拟错误场景...")
        
        try:
            # 模拟问题分析.md中的错误
            error_scenarios = [
                {
                    "symbol": "ICNT-USDT",
                    "exchange": "bybit",
                    "market": "futures",
                    "expected_error": "Qty invalid"
                },
                {
                    "symbol": "SPK-USDT", 
                    "exchange": "bybit",
                    "market": "spot",
                    "expected_error": "Order quantity has too many decimals"
                }
            ]
            
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            for scenario in error_scenarios:
                self.logger.info(f"   🧪 测试场景: {scenario['exchange']}_{scenario['symbol']}_{scenario['market']}")
                
                rule = preloader.get_trading_rule(
                    scenario['exchange'], 
                    scenario['symbol'], 
                    scenario['market']
                )
                
                if rule:
                    self.logger.info(f"   ✅ 获取成功: step_size={rule.qty_step}")
                    
                    # 检查精度是否合理
                    if rule.qty_step == 0.001:
                        self.logger.warning(f"   ⚠️ 发现默认精度0.001，可能不准确")
                else:
                    self.logger.warning(f"   ❌ 获取失败，符合预期错误场景")
                    
            self.diagnosis_results["error_scenarios"] = {
                "status": "已测试",
                "scenarios_count": len(error_scenarios)
            }
            
        except Exception as e:
            self.logger.error(f"   ❌ 模拟错误场景失败: {e}")
            self.diagnosis_results["error_scenarios"] = {
                "status": "异常",
                "error": str(e)
            }
            
    def generate_fix_recommendations(self):
        """生成修复建议"""
        self.logger.info("🎯 生成修复建议...")
        
        recommendations = []
        
        # 基于诊断结果生成建议
        if self.diagnosis_results.get("global_exchanges", {}).get("status") == "问题确认":
            recommendations.append({
                "问题": "全局交易所实例未设置",
                "修复方案": "在系统初始化时调用set_global_exchanges设置全局实例",
                "修复文件": "123/core/trading_system_initializer.py",
                "具体修复": "在initialize_all_systems方法中添加set_global_exchanges(self.exchanges)调用"
            })
            
        if self.diagnosis_results.get("precision_methods", {}).get("has_hardcode"):
            recommendations.append({
                "问题": "_get_precision_from_exchange_api_sync方法存在硬编码默认值",
                "修复方案": "修复方法实现，使用真实API调用或改进的默认值",
                "修复文件": "123/core/trading_rules_preloader.py", 
                "具体修复": "移除硬编码0.001，实现真实的API调用逻辑"
            })
            
        self.diagnosis_results["fix_recommendations"] = recommendations
        
        for i, rec in enumerate(recommendations, 1):
            self.logger.info(f"   {i}. {rec['问题']}")
            self.logger.info(f"      修复方案: {rec['修复方案']}")
            self.logger.info(f"      修复文件: {rec['修复文件']}")
            
    async def run_diagnosis(self):
        """运行完整诊断"""
        self.logger.info("🚀 开始精准诊断交易规则精度问题...")
        self.logger.info("=" * 80)
        
        try:
            await self.diagnose_trading_rules_error()
            self.generate_fix_recommendations()
            
            # 输出诊断结果
            self.logger.info("📊 诊断结果总结:")
            for key, result in self.diagnosis_results.items():
                if key != "fix_recommendations":
                    status = result.get("status", "未知")
                    self.logger.info(f"   {key}: {status}")
                    
            self.logger.info("🎯 修复建议数量: {}".format(
                len(self.diagnosis_results.get("fix_recommendations", []))
            ))
            
            return self.diagnosis_results
            
        except Exception as e:
            self.logger.error(f"❌ 诊断过程异常: {e}")
            self.logger.error(traceback.format_exc())
            return {"error": str(e)}

async def main():
    """主函数"""
    diagnosis = PrecisionErrorDiagnosis()
    results = await diagnosis.run_diagnosis()
    
    # 保存结果
    import json
    output_file = "123/diagnostic_results/precision_error_diagnosis_results.json"
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
        
    print(f"\n📄 诊断结果已保存到: {output_file}")

if __name__ == "__main__":
    asyncio.run(main())
