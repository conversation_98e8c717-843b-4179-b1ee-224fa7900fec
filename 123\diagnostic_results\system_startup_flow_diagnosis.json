{"timestamp": "2025-07-31 11:04:14", "startup_phases": {"basic_import": "SUCCESS", "exchange_init": "FAILED: No exchanges", "global_set": "FAILED: No exchanges to set", "full_init": "FAILED: Returned False", "rule_test": "ERROR: 'TradingRule' object has no attribute 'get'"}, "global_exchanges_status": {"initial": "None", "before_full_init": "None", "after_full_init": "{'gate': <exchanges.gate_exchange.GateExchange object at 0x0000013D66B06D00>, 'bybit': <exchanges.bybit_exchange.BybitExchange object at 0x0000013D66A8E100>, 'okx': <exchanges.okx_exchange.OKXExchange object at 0x0000013D66A8E160>}"}, "issues_found": ["交易所初始化失败", "交易规则测试异常: 'TradingRule' object has no attribute 'get'"], "recommendations": ["交易所初始化正常，全局实例设置机制工作正常", "WebSocket启动失败是主要问题，但不影响交易所实例", "建议：在main.py中忽略WebSocket启动失败，继续使用交易所实例"]}