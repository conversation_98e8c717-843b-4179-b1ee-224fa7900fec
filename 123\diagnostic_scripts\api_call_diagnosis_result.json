{"timestamp": "2025-07-31 14:26:21", "test_type": "API调用诊断", "api_call_tests": {"gate": {"exchange": "gate", "api_method_exists": true, "api_call_success": true, "api_call_error": null, "api_response": {"step_size": 0.0001, "min_amount": 0.0001, "max_amount": 1000000, "tick_size": 0.0001, "price_precision": 4, "amount_precision": 4, "min_notional": 1.0, "source": "gate_spot_improved_default"}, "method_inspection": {"get_instruments_info": "missing", "get_contract_info": "exists", "get_instruments": "missing"}, "response_time": 0.54, "has_wrong_hardcoded": false}, "bybit": {"exchange": "bybit", "api_method_exists": true, "api_call_success": true, "api_call_error": null, "api_response": {"step_size": 0.1, "min_amount": 0.1, "max_amount": 1000000, "tick_size": 0.0001, "price_precision": 4, "amount_precision": 1, "min_notional": 1.0, "source": "improved_default"}, "method_inspection": {"get_instruments_info": "exists", "get_contract_info": "exists", "get_instruments": "missing"}, "response_time": 10001.7, "has_wrong_hardcoded": false, "direct_api_test": {"success": true, "response_keys": ["result"], "has_result": true}}, "okx": {"exchange": "okx", "api_method_exists": true, "api_call_success": true, "api_call_error": null, "api_response": {"step_size": 0.1, "min_amount": 0.1, "max_amount": 1000000, "tick_size": 1e-05, "price_precision": 5, "amount_precision": 1, "min_notional": 1.0, "source": "improved_default"}, "method_inspection": {"get_instruments_info": "missing", "get_contract_info": "exists", "get_instruments": "exists"}, "response_time": 10007.47, "has_wrong_hardcoded": false}}, "exchange_method_tests": {}, "error_analysis": {"total_exchanges": 3, "successful_api_calls": 3, "failed_api_calls": 0, "success_rate": 1.0, "common_issues": []}, "critical_findings": []}