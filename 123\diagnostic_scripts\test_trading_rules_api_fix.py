#!/usr/bin/env python3
"""
🔥 测试交易规则API修复效果
验证SPK-USDT和ICNT-USDT等代币的精度问题是否已解决
"""

import asyncio
import sys
import os
import json
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def test_trading_rules_api_fix():
    """测试交易规则API修复效果"""
    print("🔥 测试交易规则API修复效果")
    print("=" * 60)
    
    results = {
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "test_cases": [],
        "summary": {
            "total_tests": 0,
            "api_success": 0,
            "improved_defaults": 0,
            "old_defaults": 0,
            "errors": 0
        }
    }
    
    # 测试用例：包括之前失败的代币
    test_cases = [
        ("bybit", "SPK-USDT", "spot"),
        ("bybit", "ICNT-USDT", "futures"),
        ("bybit", "BTC-USDT", "spot"),
        ("bybit", "BTC-USDT", "futures"),
        ("gate", "SPK_USDT", "spot"),
        ("gate", "ICNT_USDT", "futures"),
        ("okx", "SPK-USDT", "SPOT"),
        ("okx", "ICNT-USDT", "SWAP"),
    ]
    
    try:
        # 导入必要的模块
        from core.trading_rules_preloader import get_trading_rules_preloader
        from core.trading_system_initializer import get_global_exchanges

        preloader = get_trading_rules_preloader()

        # 🔥 清除问题代币的缓存，强制重新获取
        # 注意：缓存键格式是 {exchange}_{symbol}_{market_type}
        problem_tokens = [
            "bybit_SPK-USDT_spot",
            "bybit_ICNT-USDT_futures",
            "gate_SPK_USDT_spot",
            "gate_ICNT_USDT_futures",
            "okx_SPK-USDT_SPOT",
            "okx_ICNT-USDT_SWAP"
        ]

        print("🧹 强制清空整个缓存...")
        print(f"   📊 当前缓存中的键: {list(preloader.trading_rules.keys())}")
        print(f"   📊 缓存类型: {type(preloader.trading_rules)}")

        # 🔥 强制清空整个缓存，确保重新获取
        original_cache_size = len(preloader.trading_rules)
        preloader.trading_rules.clear()
        print(f"   🧹 强制清空整个缓存: {original_cache_size} -> {len(preloader.trading_rules)}")

        # 🔥 额外确认：检查缓存是否真的为空
        if len(preloader.trading_rules) == 0:
            print("   ✅ 缓存清空成功，将强制调用API")
        else:
            print(f"   ❌ 缓存清空失败，仍有 {len(preloader.trading_rules)} 个键")

        # 🔥 禁用预加载，防止缓存被重新填充
        original_preload_completed = preloader.preload_completed
        original_is_preloading = preloader.is_preloading
        preloader.preload_completed = False
        preloader.is_preloading = False
        print("   🚫 已禁用预加载机制，确保强制API调用")

        # 检查全局交易所实例
        global_exchanges = get_global_exchanges()
        if not global_exchanges:
            print("❌ 全局交易所实例未设置，尝试初始化...")

            # 尝试初始化系统（但不触发预加载）
            from core.trading_system_initializer import get_trading_system_initializer
            initializer = get_trading_system_initializer()

            # 🔥 只初始化交易所实例，不运行完整系统初始化（避免预加载）
            try:
                exchanges = await initializer.initialize_exchanges()
                from core.trading_system_initializer import set_global_exchanges
                set_global_exchanges(exchanges)
            except Exception as e:
                print(f"⚠️ 交易所初始化警告: {e}")

            global_exchanges = get_global_exchanges()
            if not global_exchanges:
                print("❌ 无法初始化全局交易所实例")
                return results
        
        print(f"✅ 全局交易所实例: {list(global_exchanges.keys())}")
        print()
        
        # 测试每个用例
        for exchange_name, symbol, market_type in test_cases:
            results["summary"]["total_tests"] += 1
            test_result = {
                "exchange": exchange_name,
                "symbol": symbol,
                "market_type": market_type,
                "status": "unknown",
                "step_size": None,
                "source": None,
                "error": None,
                "execution_time_ms": 0
            }
            
            print(f"🔍 测试: {exchange_name} {symbol} {market_type}")
            
            try:
                start_time = time.time()
                
                # 获取交易规则
                trading_rule = preloader.get_trading_rule(exchange_name, symbol, market_type)
                
                execution_time = (time.time() - start_time) * 1000
                test_result["execution_time_ms"] = round(execution_time, 2)
                
                if trading_rule:
                    test_result["status"] = "success"
                    test_result["step_size"] = float(trading_rule.qty_step)
                    test_result["source"] = trading_rule.source if hasattr(trading_rule, 'source') else "unknown"
                    
                    # 分类结果
                    if "api_real" in test_result["source"]:
                        results["summary"]["api_success"] += 1
                        status_icon = "✅"
                    elif "improved_default" in test_result["source"]:
                        results["summary"]["improved_defaults"] += 1
                        status_icon = "🔧"
                    elif test_result["source"] == "default":
                        results["summary"]["old_defaults"] += 1
                        status_icon = "⚠️"
                    else:
                        status_icon = "❓"
                    
                    print(f"   {status_icon} 成功: step_size={test_result['step_size']}, source={test_result['source']}")
                    print(f"   ⏱️ 执行时间: {execution_time:.2f}ms")
                    
                    # 特别检查之前失败的代币
                    if symbol in ["SPK-USDT", "ICNT-USDT"]:
                        if test_result["step_size"] == 0.001:
                            print(f"   ⚠️ 警告: {symbol} 仍在使用可能导致错误的步长 0.001")
                        else:
                            print(f"   🎯 修复确认: {symbol} 使用新的步长 {test_result['step_size']}")
                    
                else:
                    test_result["status"] = "failed"
                    test_result["error"] = "No trading rule returned"
                    results["summary"]["errors"] += 1
                    print(f"   ❌ 失败: 未返回交易规则")
                    
            except Exception as e:
                test_result["status"] = "error"
                test_result["error"] = str(e)
                results["summary"]["errors"] += 1
                print(f"   ❌ 错误: {e}")
            
            results["test_cases"].append(test_result)
            print()
        
        # 输出总结
        print("📊 测试总结")
        print("=" * 40)
        print(f"总测试数: {results['summary']['total_tests']}")
        print(f"API成功: {results['summary']['api_success']}")
        print(f"改进默认值: {results['summary']['improved_defaults']}")
        print(f"旧默认值: {results['summary']['old_defaults']}")
        print(f"错误: {results['summary']['errors']}")
        
        # 计算成功率
        success_rate = ((results['summary']['api_success'] + results['summary']['improved_defaults']) / 
                       results['summary']['total_tests'] * 100)
        print(f"成功率: {success_rate:.1f}%")
        
        # 保存结果
        results_file = project_root / "diagnostic_results" / "trading_rules_api_fix_test.json"
        results_file.parent.mkdir(exist_ok=True)
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📁 结果已保存到: {results_file}")
        
        # 关键修复验证
        print("\n🎯 关键修复验证")
        print("=" * 40)
        
        spk_test = next((t for t in results["test_cases"] if t["symbol"] == "SPK-USDT" and t["exchange"] == "bybit"), None)
        icnt_test = next((t for t in results["test_cases"] if t["symbol"] == "ICNT-USDT" and t["exchange"] == "bybit"), None)
        
        if spk_test and spk_test["status"] == "success":
            if spk_test["step_size"] != 0.001:
                print(f"✅ SPK-USDT修复成功: 新步长 {spk_test['step_size']} (来源: {spk_test['source']})")
            else:
                print(f"⚠️ SPK-USDT仍使用旧步长: {spk_test['step_size']}")
        
        if icnt_test and icnt_test["status"] == "success":
            if icnt_test["step_size"] != 0.001:
                print(f"✅ ICNT-USDT修复成功: 新步长 {icnt_test['step_size']} (来源: {icnt_test['source']})")
            else:
                print(f"⚠️ ICNT-USDT仍使用旧步长: {icnt_test['step_size']}")

        # 🔥 恢复预加载状态
        try:
            preloader.preload_completed = original_preload_completed
            preloader.is_preloading = original_is_preloading
            print("\n🔄 已恢复预加载机制状态")
        except:
            pass

    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        results["error"] = str(e)

        # 🔥 确保在异常情况下也恢复预加载状态
        try:
            preloader.preload_completed = original_preload_completed
            preloader.is_preloading = original_is_preloading
        except:
            pass
    
    return results

if __name__ == "__main__":
    asyncio.run(test_trading_rules_api_fix())
